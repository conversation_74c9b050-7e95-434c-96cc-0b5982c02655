"use client";

import { Sidebar, SidebarClose } from "lucide-react";
import { <PERSON><PERSON> } from "../ui/button";
import { useSidebar } from "../ui/sidebar";
import { cn } from "@/lib/utils";

export default ({
  title,
  description,
  additional,
  className = "",
  ...props
}: {
  title: string;
  description: string;
  additional?: React.ReactNode;
  className?: string;
  props?: React.HTMLAttributes<HTMLDivElement>;
}) => {
  const { toggleSidebar, open } = useSidebar();
  return (
    <div
      className={cn(
        "z-20 sticky -top-2 md:-top-4 py-2 backdrop-blur-[2px] shadow-2xl shadow-background/50 bg-gradient-to-b from-background/90 to-background/30 flex flex-col gap-2",
        className
      )}
      {...props}>
      <div className="gap-2 flex flex-row items-center">
        <Button onClick={toggleSidebar} size={"icon"}>
          {open ? <SidebarClose /> : <Sidebar />}
        </Button>
        <div className="flex flex-col truncate">
          <h1 className="text-3xl font-bold text-foreground truncate">{title}</h1>
          <p className="text-foreground truncate">{description}</p>
        </div>
      </div>
      {additional}
    </div>
  );
};
