"use client";

import React from "react";
import { Button } from "../ui/button";
import * as P from "@/components/ui/popover";
import { FriendData } from "./friends-client";
import Avatar from "../layout/avatar";
import * as D from "@/components/ui/dialog";

export default ({
  friends,
  onDenyRequest,
  loading,
}: {
  friends: FriendData[];
  onDenyRequest: (senderId: string, receiverId: string) => Promise<boolean>;
  loading: boolean;
}) => {
  if (loading) return <div className="text-center text-muted-foreground">Loading friends...</div>;
  if (friends.length === 0)
    return <div className="text-center text-muted-foreground">No friends yet. Add some friends to get started!</div>;
  return (
    <ul className="flex flex-col gap-2 w-full">
      {friends.map((f) => (
        <P.Popover key={f.id}>
          <P.PopoverTrigger asChild>
            <Button variant="default" className="flex items-center justify-start gap-2 rounded-lg cursor-pointer py-8">
              <Avatar img={f.image} />
              <span className="text-lg ml-2 truncate font-medium">{f.name}</span>
            </Button>
          </P.PopoverTrigger>
          <P.PopoverContent
            align="center"
            sideOffset={16}
            side="top"
            className="w-full grid grid-cols-1 md:grid-cols-[4rem_1fr] justify-center items-center gap-2">
            <Avatar img={f.image} size="md" />
            <div className="w-[calc(100%-4rem)]">
              <h1 className="text-shadow-2xs w-full truncate flex flex-row gap-1 items-center">
                <span className="select-none">Name:</span>
                <span className={`text-xs select-all`}>{f.name}</span>
              </h1>
              <h2 className="text-shadow-2xs w-full truncate flex flex-row gap-1 items-center">
                <span className="select-none">Email:</span>
                <span className={`text-xs select-all`}>{f.email}</span>
              </h2>
              <h3 className="text-shadow-2xs w-full flex flex-row gap-1 items-center">
                <span className="select-none">Bio:</span>
                <span className={`text-xs select-all ${f.bio ? "text-inherit" : "text-muted-foreground"}`}>
                  {f.bio || "User has no bio"}
                </span>
              </h3>
            </div>
            <div className="col-span-2 flex flex-row flex-wrap justify-start gap-2">
              <Button size="icon"></Button>
              <Button size="icon"></Button>
              <Button size="icon"></Button>
            </div>
          </P.PopoverContent>
        </P.Popover>
      ))}
    </ul>
  );
};
